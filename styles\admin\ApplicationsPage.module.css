.applicationsPage {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 16px;
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 16px;
}

.backButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6c757d;
}

.backButton:hover {
  background-color: #e9ecef;
  color: #495057;
  transform: translateX(-2px);
}

.backButton svg {
  width: 20px;
  height: 20px;
}

.header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0 0 4px 0;
}

.subtitle {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #d32f2f;
}

/* Statistics Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.statCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
}

.statIcon {
  font-size: 2rem;
  opacity: 0.9;
}

.statContent {
  flex: 1;
}

.statNumber {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .applicationsPage {
    padding: 16px;
    margin: 0;
    border-radius: 0;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .titleSection {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .statCard {
    padding: 16px;
  }

  .statNumber {
    font-size: 1.5rem;
  }

  .statIcon {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .header h2 {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 0.8rem;
  }

  .statCard {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .statContent {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
