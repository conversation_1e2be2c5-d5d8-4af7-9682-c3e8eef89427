import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import ApplicationsList from '@/components/admin/users/ApplicationsList'
import ApplicationReviewModal from '@/components/admin/users/ApplicationReviewModal'
import { useAuth } from '@/contexts/AuthContext'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/ApplicationsPage.module.css'

export default function ApplicationsPage() {
  const router = useRouter()
  const { hasAdminAccess } = useAuth()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [applications, setApplications] = useState([])
  const [selectedApplication, setSelectedApplication] = useState(null)
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    search: ''
  })
  const [stats, setStats] = useState({
    pending: 0,
    under_review: 0,
    approved: 0,
    rejected: 0,
    total: 0
  })

  // Fetch applications on component mount and when filters change
  useEffect(() => {
    fetchApplications()
  }, [filters])

  const fetchApplications = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔍 Fetching applications with filters:', filters)

      // Build query parameters
      const queryParams = new URLSearchParams()
      if (filters.status !== 'all') queryParams.append('status', filters.status)
      if (filters.type !== 'all') queryParams.append('type', filters.type)
      if (filters.search) queryParams.append('search', filters.search)

      const response = await fetch(`/api/admin/users/applications?${queryParams.toString()}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      console.log('✅ Applications fetched successfully:', data.applications?.length || 0)
      
      setApplications(data.applications || [])
      setStats(data.stats || stats)
    } catch (error) {
      console.error('❌ Error fetching applications:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleReviewApplication = (application) => {
    console.log('📝 Opening review modal for application:', application.id)
    setSelectedApplication(application)
    setShowReviewModal(true)
  }

  const handleReviewSubmit = async (applicationId, status, notes) => {
    try {
      console.log('📋 Submitting review:', { applicationId, status, notes })

      const response = await fetch(`/api/admin/users/applications/${applicationId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, notes })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      console.log('✅ Review submitted successfully')
      
      // Close modal and refresh applications
      setShowReviewModal(false)
      setSelectedApplication(null)
      await fetchApplications()

      // Show success message
      alert(`Application ${status === 'approved' ? 'approved' : 'rejected'} successfully!`)
    } catch (error) {
      console.error('❌ Error submitting review:', error)
      alert(`Error submitting review: ${error.message}`)
    }
  }

  const handleFilterChange = (newFilters) => {
    console.log('🔄 Updating filters:', newFilters)
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleBackToUsers = () => {
    router.push('/admin/users')
  }

  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="Application Review">
        <div className={styles.applicationsPage}>
          <div className={styles.header}>
            <div className={styles.titleSection}>
              <button
                className={styles.backButton}
                onClick={handleBackToUsers}
                title="Back to User Management"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M19 12H5M12 19l-7-7 7-7"/>
                </svg>
              </button>
              <div>
                <h2>Artist & Braider Applications</h2>
                <p className={styles.subtitle}>
                  Review and manage applications from potential team members
                </p>
              </div>
            </div>
          </div>

          {error && (
            <div className={styles.error}>
              <strong>Error:</strong> {safeRender(error, 'Unknown error')}
            </div>
          )}

          {/* Statistics Cards */}
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <div className={styles.statIcon}>⏳</div>
              <div className={styles.statContent}>
                <div className={styles.statNumber}>{safeRender(stats.pending, '0')}</div>
                <div className={styles.statLabel}>Pending Review</div>
              </div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statIcon}>👀</div>
              <div className={styles.statContent}>
                <div className={styles.statNumber}>{safeRender(stats.under_review, '0')}</div>
                <div className={styles.statLabel}>Under Review</div>
              </div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statIcon}>✅</div>
              <div className={styles.statContent}>
                <div className={styles.statNumber}>{safeRender(stats.approved, '0')}</div>
                <div className={styles.statLabel}>Approved</div>
              </div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statIcon}>❌</div>
              <div className={styles.statContent}>
                <div className={styles.statNumber}>{safeRender(stats.rejected, '0')}</div>
                <div className={styles.statLabel}>Rejected</div>
              </div>
            </div>
          </div>

          {/* Applications List */}
          <ApplicationsList
            applications={applications}
            loading={loading}
            filters={filters}
            onFilterChange={handleFilterChange}
            onReviewApplication={handleReviewApplication}
          />

          {/* Review Modal */}
          {showReviewModal && selectedApplication && (
            <ApplicationReviewModal
              application={selectedApplication}
              onClose={() => {
                setShowReviewModal(false)
                setSelectedApplication(null)
              }}
              onSubmit={handleReviewSubmit}
            />
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
