import { useState } from 'react'
import ApplicationStatusBadge from './ApplicationStatusBadge'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/users/ApplicationReviewModal.module.css'

export default function ApplicationReviewModal({ application, onClose, onSubmit }) {
  const [reviewStatus, setReviewStatus] = useState('')
  const [reviewNotes, setReviewNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!reviewStatus) {
      alert('Please select a review status')
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(application.id, reviewStatus, reviewNotes)
    } catch (error) {
      console.error('Error submitting review:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString('en-AU', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      return 'Invalid Date'
    }
  }

  const formatExperience = (years) => {
    if (!years) return 'Not specified'
    return `${years} year${years !== 1 ? 's' : ''}`
  }

  const formatSpecializations = (specializations) => {
    if (!specializations || !Array.isArray(specializations)) return 'None specified'
    return specializations.join(', ')
  }

  const formatAvailability = (preferences) => {
    if (!preferences || typeof preferences !== 'object') return 'Not specified'
    
    const parts = []
    if (preferences.weekdays && Array.isArray(preferences.weekdays) && preferences.weekdays.length > 0) {
      parts.push(`Weekdays: ${preferences.weekdays.join(', ')}`)
    }
    if (preferences.weekends) parts.push('Weekends available')
    if (preferences.evenings) parts.push('Evenings available')
    if (preferences.flexible) parts.push('Flexible schedule')
    
    return parts.length > 0 ? parts.join(', ') : 'Not specified'
  }

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2>Review Application</h2>
          <button
            className={styles.closeButton}
            onClick={onClose}
            title="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        <div className={styles.modalBody}>
          {/* Application Details */}
          <div className={styles.applicationDetails}>
            <div className={styles.detailSection}>
              <h3>Applicant Information</h3>
              <div className={styles.detailGrid}>
                <div className={styles.detailItem}>
                  <label>Name:</label>
                  <span>{safeRender(application.userName, 'Unknown')}</span>
                </div>
                <div className={styles.detailItem}>
                  <label>Phone:</label>
                  <span>{safeRender(application.userPhone, 'Not provided')}</span>
                </div>
                <div className={styles.detailItem}>
                  <label>Application Type:</label>
                  <span className={`${styles.typeBadge} ${styles[application.applicationType]}`}>
                    {safeRender(application.applicationType, 'Unknown')}
                  </span>
                </div>
                <div className={styles.detailItem}>
                  <label>Current Status:</label>
                  <ApplicationStatusBadge status={application.status} />
                </div>
              </div>
            </div>

            <div className={styles.detailSection}>
              <h3>Experience & Skills</h3>
              <div className={styles.detailGrid}>
                <div className={styles.detailItem}>
                  <label>Years of Experience:</label>
                  <span>{formatExperience(application.experienceYears)}</span>
                </div>
                <div className={styles.detailItem}>
                  <label>Service Specializations:</label>
                  <span>{formatSpecializations(application.serviceSpecializations)}</span>
                </div>
                {application.portfolioUrl && (
                  <div className={styles.detailItem}>
                    <label>Portfolio:</label>
                    <a 
                      href={application.portfolioUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className={styles.portfolioLink}
                    >
                      View Portfolio →
                    </a>
                  </div>
                )}
              </div>
            </div>

            <div className={styles.detailSection}>
              <h3>Availability</h3>
              <div className={styles.detailItem}>
                <span>{formatAvailability(application.availabilityPreferences)}</span>
              </div>
            </div>

            {application.previousExperience && (
              <div className={styles.detailSection}>
                <h3>Previous Experience</h3>
                <div className={styles.textContent}>
                  {safeRender(application.previousExperience, 'Not provided')}
                </div>
              </div>
            )}

            {application.references && (
              <div className={styles.detailSection}>
                <h3>References</h3>
                <div className={styles.textContent}>
                  {safeRender(application.references, 'Not provided')}
                </div>
              </div>
            )}

            <div className={styles.detailSection}>
              <h3>Application Timeline</h3>
              <div className={styles.detailGrid}>
                <div className={styles.detailItem}>
                  <label>Submitted:</label>
                  <span>{formatDate(application.createdAt)}</span>
                </div>
                <div className={styles.detailItem}>
                  <label>Last Updated:</label>
                  <span>{formatDate(application.updatedAt)}</span>
                </div>
                {application.reviewedAt && (
                  <div className={styles.detailItem}>
                    <label>Last Reviewed:</label>
                    <span>{formatDate(application.reviewedAt)}</span>
                  </div>
                )}
              </div>
            </div>

            {application.reviewNotes && (
              <div className={styles.detailSection}>
                <h3>Previous Review Notes</h3>
                <div className={styles.textContent}>
                  {safeRender(application.reviewNotes, 'No previous notes')}
                </div>
              </div>
            )}
          </div>

          {/* Review Form */}
          <form onSubmit={handleSubmit} className={styles.reviewForm}>
            <div className={styles.formSection}>
              <h3>Review Decision</h3>
              
              <div className={styles.statusOptions}>
                <label className={styles.statusOption}>
                  <input
                    type="radio"
                    name="reviewStatus"
                    value="under_review"
                    checked={reviewStatus === 'under_review'}
                    onChange={(e) => setReviewStatus(e.target.value)}
                  />
                  <span className={styles.statusLabel}>
                    <span className={styles.statusIcon}>👀</span>
                    Mark as Under Review
                  </span>
                </label>

                <label className={styles.statusOption}>
                  <input
                    type="radio"
                    name="reviewStatus"
                    value="approved"
                    checked={reviewStatus === 'approved'}
                    onChange={(e) => setReviewStatus(e.target.value)}
                  />
                  <span className={styles.statusLabel}>
                    <span className={styles.statusIcon}>✅</span>
                    Approve Application
                  </span>
                </label>

                <label className={styles.statusOption}>
                  <input
                    type="radio"
                    name="reviewStatus"
                    value="rejected"
                    checked={reviewStatus === 'rejected'}
                    onChange={(e) => setReviewStatus(e.target.value)}
                  />
                  <span className={styles.statusLabel}>
                    <span className={styles.statusIcon}>❌</span>
                    Reject Application
                  </span>
                </label>
              </div>

              <div className={styles.notesSection}>
                <label htmlFor="reviewNotes">Review Notes:</label>
                <textarea
                  id="reviewNotes"
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  placeholder="Add notes about your review decision..."
                  className={styles.notesTextarea}
                  rows="4"
                />
              </div>
            </div>

            <div className={styles.formActions}>
              <button
                type="button"
                onClick={onClose}
                className={styles.cancelButton}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className={styles.submitButton}
                disabled={isSubmitting || !reviewStatus}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Review'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
